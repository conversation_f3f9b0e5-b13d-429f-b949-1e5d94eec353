"""Preview widget for displaying images and processing results."""

import cv2
import numpy as np
from typing import Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QScrollArea, QPushButton
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPixmap, QImage

from ..image_processing.centering import CenteringResult


class ImageLabel(QLabel):
    """Custom QLabel for displaying images with zoom and pan capabilities."""
    
    clicked = Signal()
    
    def __init__(self):
        super().__init__()
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("border: 1px solid gray;")
        self.setMinimumSize(400, 300)
        self.setText("No image loaded")
        
        # Zoom and pan state
        self.scale_factor = 1.0
        self.original_pixmap = None
    
    def set_image(self, image: np.ndarray):
        """Set image to display.

        Args:
            image: Image as numpy array in BGR format (8-bit or 16-bit)
        """
        # Convert 16-bit to 8-bit for display if needed
        if image.dtype == np.uint16:
            from ..image_processing.raw_processor import RawProcessor
            raw_processor = RawProcessor()
            display_image = raw_processor.prepare_for_display(image)
        else:
            display_image = image

        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB)

        # Convert to QImage (now guaranteed to be 8-bit)
        height, width, channel = rgb_image.shape
        bytes_per_line = 3 * width
        q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)

        # Convert to QPixmap and store original
        self.original_pixmap = QPixmap.fromImage(q_image)

        # Automatically fit image to window when loaded
        self.fit_to_window()
        self.update_display()
    
    def update_display(self):
        """Update the displayed image with current scale factor."""
        if self.original_pixmap:
            scaled_pixmap = self.original_pixmap.scaled(
                self.original_pixmap.size() * self.scale_factor,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.setPixmap(scaled_pixmap)
    
    def zoom_in(self):
        """Zoom in on the image."""
        self.scale_factor = min(self.scale_factor * 1.25, 5.0)
        self.update_display()
    
    def zoom_out(self):
        """Zoom out on the image."""
        self.scale_factor = max(self.scale_factor * 0.8, 0.1)
        self.update_display()
    
    def reset_zoom(self):
        """Reset zoom to fit the widget."""
        if self.original_pixmap:
            widget_size = self.size()
            pixmap_size = self.original_pixmap.size()

            scale_x = widget_size.width() / pixmap_size.width()
            scale_y = widget_size.height() / pixmap_size.height()
            self.scale_factor = min(scale_x, scale_y, 1.0)

            self.update_display()

    def fit_to_window(self):
        """Automatically fit image to window when loaded."""
        if self.original_pixmap:
            # Get the current widget size, but handle case where widget isn't fully initialized
            widget_size = self.size()

            # If widget size is not available or too small, use minimum size
            if widget_size.width() <= 1 or widget_size.height() <= 1:
                widget_size = self.minimumSize()

            pixmap_size = self.original_pixmap.size()

            # Calculate scale factor to fit image within widget while maintaining aspect ratio
            scale_x = widget_size.width() / pixmap_size.width()
            scale_y = widget_size.height() / pixmap_size.height()

            # Use the smaller scale factor to ensure image fits completely
            # Don't scale up beyond original size (max 1.0)
            self.scale_factor = min(scale_x, scale_y, 1.0)
    
    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class PreviewWidget(QWidget):
    """Widget for previewing images and processing results."""
    
    def __init__(self):
        super().__init__()
        self.original_image = None
        self.processed_image = None
        self.detection_data = None
        self.centering_result = None
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize user interface."""
        layout = QVBoxLayout(self)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.show_original_btn = QPushButton("Show Original")
        self.show_original_btn.clicked.connect(self.show_original)
        self.show_original_btn.setEnabled(False)
        controls_layout.addWidget(self.show_original_btn)
        
        self.show_processed_btn = QPushButton("Show Processed")
        self.show_processed_btn.clicked.connect(self.show_processed)
        self.show_processed_btn.setEnabled(False)
        controls_layout.addWidget(self.show_processed_btn)

        self.show_detection_btn = QPushButton("Show Detection")
        self.show_detection_btn.clicked.connect(self.show_detection)
        self.show_detection_btn.setEnabled(False)
        controls_layout.addWidget(self.show_detection_btn)

        self.show_visualization_btn = QPushButton("Show Visualization")
        self.show_visualization_btn.clicked.connect(self.show_visualization)
        self.show_visualization_btn.setEnabled(False)
        controls_layout.addWidget(self.show_visualization_btn)



        controls_layout.addStretch()
        
        # Zoom controls
        self.zoom_in_btn = QPushButton("Zoom In")
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        controls_layout.addWidget(self.zoom_in_btn)
        
        self.zoom_out_btn = QPushButton("Zoom Out")
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        controls_layout.addWidget(self.zoom_out_btn)
        
        self.reset_zoom_btn = QPushButton("Fit to Window")
        self.reset_zoom_btn.clicked.connect(self.reset_zoom)
        controls_layout.addWidget(self.reset_zoom_btn)
        
        layout.addLayout(controls_layout)
        
        # Image display area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setAlignment(Qt.AlignCenter)
        
        self.image_label = ImageLabel()
        scroll_area.setWidget(self.image_label)
        
        layout.addWidget(scroll_area)
        
        # Info panel
        info_layout = QHBoxLayout()
        
        self.info_label = QLabel("No image loaded")
        self.info_label.setStyleSheet("padding: 5px; background-color: #f0f0f0;")
        info_layout.addWidget(self.info_label)
        
        layout.addLayout(info_layout)
    
    def set_original_image(self, image: np.ndarray):
        """Set the original image.
        
        Args:
            image: Original image as numpy array
        """
        self.original_image = image.copy()
        self.image_label.set_image(image)
        self.show_original_btn.setEnabled(True)
        
        # Update info
        height, width = image.shape[:2]
        self.info_label.setText(f"Original image: {width}x{height}")
    
    def set_processed_image(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any], 
        centering_result: CenteringResult
    ):
        """Set the processed image and results.
        
        Args:
            image: Processed image as numpy array
            detection: Detection data
            centering_result: Centering result
        """
        self.processed_image = image.copy()
        self.detection_data = detection
        self.centering_result = centering_result
        
        self.show_processed_btn.setEnabled(True)
        self.show_detection_btn.setEnabled(True)
        self.show_visualization_btn.setEnabled(True)

        # Show processed image by default
        self.show_processed()


    
    def show_original(self):
        """Show the original image."""
        if self.original_image is not None:
            self.image_label.set_image(self.original_image)
            height, width = self.original_image.shape[:2]
            self.info_label.setText(f"Original image: {width}x{height}")
    
    def show_processed(self):
        """Show the processed image."""
        if self.processed_image is not None:
            self.image_label.set_image(self.processed_image)
            height, width = self.processed_image.shape[:2]
            
            info_text = f"Processed image: {width}x{height}"
            if self.centering_result:
                info_text += f" | Centering confidence: {self.centering_result.confidence:.3f}"
            if self.detection_data:
                info_text += f" | Detection confidence: {self.detection_data['confidence']:.3f}"
            
            self.info_label.setText(info_text)

    def show_detection(self):
        """Show the original image with detection overlays."""
        if self.original_image is not None and self.detection_data is not None and self.centering_result is not None:
            # Create detection visualization using the improved method
            from ..image_processing.centering import PhotoCenterer

            centerer = PhotoCenterer()

            # Use the new original detection visualization method
            vis_image = centerer.visualize_original_detection(
                self.original_image, self.detection_data, self.centering_result
            )

            self.image_label.set_image(vis_image)

            height, width = vis_image.shape[:2]
            self.info_label.setText(f"Detection view: {width}x{height} | Confidence: {self.detection_data['confidence']:.3f}")

    def show_visualization(self):
        """Show visualization with detection and centering overlays."""
        if self.processed_image is not None and self.centering_result is not None:
            # Create visualization using the improved centering visualization
            from ..image_processing.centering import PhotoCenterer

            centerer = PhotoCenterer()

            # Use the improved visualization method
            vis_image = centerer.visualize_centering(self.centering_result)

            # Add detection keypoints if available
            if self.detection_data and self.detection_data.get('keypoints'):
                # Draw keypoints on the cropped image
                crop_box = self.centering_result.crop_box
                keypoints = self.detection_data['keypoints']

                for name, (x, y) in keypoints.items():
                    # Adjust coordinates to cropped image
                    adj_x = x - crop_box[0]
                    adj_y = y - crop_box[1]

                    # Check if keypoint is within cropped area
                    if 0 <= adj_x < vis_image.shape[1] and 0 <= adj_y < vis_image.shape[0]:
                        # Use bright, highly visible colors for different keypoint types
                        if 'eye' in name or 'nose' in name:
                            color = (255, 20, 147)  # Hot Pink for face - highly visible
                        elif 'shoulder' in name:
                            color = (0, 255, 0)  # Bright Green for shoulders
                        elif 'hip' in name:
                            color = (0, 165, 255)  # Bright Orange for hips
                        else:
                            color = (0, 255, 255)  # Bright Cyan for others

                        cv2.circle(vis_image, (adj_x, adj_y), 4, color, -1)
                        cv2.circle(vis_image, (adj_x, adj_y), 6, (255, 255, 255), 2)

            self.image_label.set_image(vis_image)

            height, width = vis_image.shape[:2]
            self.info_label.setText(f"Visualization: {width}x{height} | Method: {self.centering_result.method_used}")
    
    def zoom_in(self):
        """Zoom in on the current image."""
        self.image_label.zoom_in()
    
    def zoom_out(self):
        """Zoom out on the current image."""
        self.image_label.zoom_out()
    
    def reset_zoom(self):
        """Reset zoom to fit window."""
        self.image_label.reset_zoom()


